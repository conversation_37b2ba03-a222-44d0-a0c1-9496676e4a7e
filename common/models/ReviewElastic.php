<?php

namespace common\models;

use yii\elasticsearch\ActiveRecord;

/**
 * ReviewElastic represents the elasticsearch index for reviews
 */
class ReviewElastic extends ActiveRecord
{
    /**
     * @return array the list of attributes for this record
     */
    public function attributes()
    {
        return [
            'review_id',
            'college_id',
            'college_name',
            'college_slug',
            'college_display_name',
            'college_city_id',
            'college_city_name',
            'city_slug',
            'college_state_id',
            'college_state_name',
            'state_slug',
            'course_id',
            'course_name',
            'course_slug',
            'stream_id',
            'stream_name',
            'stream_slug',
            'review_slug',
            'student_name',
            'review_overall_rating',
            'batch',
            'review_created_at',
            'status'
        ];
    }

    /**
     * @return string the name of the index associated with this ActiveRecord class.
     */
    public static function index()
    {
        return 'reviews';
    }

    /**
     * @return string the name of the type of this ActiveRecord class.
     */
    public static function type()
    {
        return '_doc';
    }

    /**
     * Define the mapping for this elasticsearch type
     */
    public static function mapping()
    {
        return [
            'properties' => [
                'review_id' => ['type' => 'integer'],
                'college_id' => ['type' => 'integer'],
                'college_name' => [
                    'type' => 'text',
                    'analyzer' => 'standard',
                    'fields' => [
                        'keyword' => ['type' => 'keyword']
                    ]
                ],
                'college_slug' => ['type' => 'keyword'],
                'college_display_name' => [
                    'type' => 'text',
                    'analyzer' => 'standard',
                    'fields' => [
                        'keyword' => ['type' => 'keyword']
                    ]
                ],
                'college_city_id' => ['type' => 'integer'],
                'college_city_name' => [
                    'type' => 'text',
                    'analyzer' => 'standard',
                    'fields' => [
                        'keyword' => ['type' => 'keyword']
                    ]
                ],
                'city_slug' => ['type' => 'keyword'],
                'college_state_id' => ['type' => 'integer'],
                'college_state_name' => [
                    'type' => 'text',
                    'analyzer' => 'standard',
                    'fields' => [
                        'keyword' => ['type' => 'keyword']
                    ]
                ],
                'state_slug' => ['type' => 'keyword'],
                'course_id' => ['type' => 'integer'],
                'course_name' => [
                    'type' => 'text',
                    'analyzer' => 'standard',
                    'fields' => [
                        'keyword' => ['type' => 'keyword']
                    ]
                ],
                'course_slug' => ['type' => 'keyword'],
                'stream_id' => ['type' => 'integer'],
                'stream_name' => [
                    'type' => 'text',
                    'analyzer' => 'standard',
                    'fields' => [
                        'keyword' => ['type' => 'keyword']
                    ]
                ],
                'stream_slug' => ['type' => 'keyword'],
                'student_name' => [
                    'type' => 'text',
                    'analyzer' => 'standard',
                    'fields' => [
                        'keyword' => ['type' => 'keyword']
                    ]
                ],
                'review_slug' => ['type' => 'keyword'],
                'review_overall_rating' => ['type' => 'float'],
                'batch' => ['type' => 'integer'],
                'review_created_at' => ['type' => 'date', 'format' => 'yyyy-MM-dd HH:mm:ss'],
                'status' => ['type' => 'integer'],
            ]
        ];
    }

    /**
     * Create the index with mapping
     */
    public static function createIndex()
    {
        $db = static::getDb();
        $command = $db->createCommand();

        // Delete index if exists
        try {
            $command->deleteIndex(static::index());
            echo "Deleted existing reviews index.\n";
        } catch (\Exception $e) {
            // Index doesn't exist, continue
            unset($e); // Suppress unused variable warning
            echo "No existing index to delete.\n";
        }

        // Create index with mapping
        $command->createIndex(static::index(), [
            'settings' => [
                'number_of_shards' => 1,
                'number_of_replicas' => 0,
            ],
            'mappings' => static::mapping()
        ]);

        echo "Reviews index created successfully.\n";
    }

    /**
     * Update mapping for existing index
     */
    public static function updateMapping()
    {
        $db = static::getDb();
        $command = $db->createCommand();
        $command->setMapping(static::index(), static::type(), static::mapping());
    }

    /**
     * Delete this model's index
     */
    public static function deleteIndex()
    {
        $db = static::getDb();
        $command = $db->createCommand();
        $command->deleteIndex(static::index(), static::type());
    }
}
