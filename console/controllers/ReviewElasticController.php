<?php

namespace console\controllers;

use common\helpers\CollegeHelper;
use common\models\Review;
use common\models\ReviewElastic;
use common\services\ReviewService;
use yii\console\Controller;

/**
 * Console controller for indexing reviews into Elasticsearch
 */
class ReviewElasticController extends Controller
{
    /**
     * Index all reviews into Elasticsearch
     */
    public function actionIndexAll()
    {
        echo "Starting review indexing...\n";

        // Create/update index mapping
        ReviewElastic::updateMapping();

        echo "Index mapping updated.\n";

        // Get all approved reviews with related data
        $queries = Review::find()->active();

        foreach ($queries->batch() as $reviews) {
            foreach ($reviews as $review) {
                $this->updateReview($review);
            }
        }

        echo "Completed indexing reviews.\n";
    }

    /**
     * Delete review from Elasticsearch
     */
    public function actionDeleteReview($reviewId)
    {
        try {
            $reviewElastic = ReviewElastic::get($reviewId);
            if ($reviewElastic) {
                $reviewElastic->delete();
                echo "Review {$reviewId} deleted from Elasticsearch.\n";
            } else {
                echo "Review {$reviewId} not found in Elasticsearch.\n";
            }
        } catch (\Exception $e) {
            echo "Error deleting review {$reviewId}: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Index a single review data
     */
    private function updateReview(Review $review)
    {
        try {
            $reviewService = new ReviewService();

            $reviewCategoryRatings = $reviewService->getReviewCategoryRating($review->id);

            // Calculate overall rating
            $overallRating = CollegeHelper::getTotalRating($reviewCategoryRatings);

            // Calculate batch from admission year
            $batch = $review->admission_year ? (int)$review->admission_year : 0;

            // Create or update Elasticsearch document
            try {
                $reviewElastic = ReviewElastic::get($review->id);
            } catch (\Exception $e) {
                $reviewElastic = null;
            }

            if (!$reviewElastic) {
                $reviewElastic = new ReviewElastic();
                $reviewElastic->_id = $review->id;
            }

            // Set attributes
            $reviewElastic->review_id = $review->id;
            $reviewElastic->college_id = $review->college_id ?? '';
            $reviewElastic->college_name = $review->college->name ?? '';
            $reviewElastic->college_slug = $review->college->slug ?? '';
            $reviewElastic->college_display_name = $review->college->display_name ?? '';
            $reviewElastic->college_city_id = $review->college->city->id ?? '';
            $reviewElastic->college_city_name = $review->college->city->name ?? '';
            $reviewElastic->city_slug = $review->college->city->slug ?? '';

            $reviewElastic->college_state_id = !empty($review->college) ? $review->college->city->state->id : '';
            $reviewElastic->college_state_name = !empty($review->college) ? $review->college->city->state->name : '';
            $reviewElastic->state_slug = !empty($review->college) ? $review->college->city->state->slug : '';

            $reviewElastic->course_id = $review->course_id ?? '';
            $reviewElastic->course_name = $review->course->name ?? '';
            $reviewElastic->course_slug = $review->course->slug ?? '';

            $reviewElastic->stream_id = $review->course->stream->id ?? '';
            $reviewElastic->stream_name = $review->course->stream->name ?? '';
            $reviewElastic->stream_slug = $review->course->stream->slug ?? '';

            $reviewElastic->review_slug = $review->slug ?? '';
            $reviewElastic->student_name = $review->user->name ?? '';

            $reviewElastic->review_overall_rating = $overallRating;

            $reviewElastic->batch = $batch;
            $reviewElastic->review_created_at = $review->created_at ?? '';

            $reviewElastic->status = $review->status;

            if ($reviewElastic->save()) {
                echo "Review {$review->id} indexed successfully.\n";
            }
        } catch (\Exception $e) {
            echo "Error indexing review {$review->id}: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Create index and mapping
     */
    public function actionCreateIndex()
    {
        ReviewElastic::createIndex();
        echo "Review index created successfully.\n";
    }

    /**
     * Update mapping
     */
    public function actionUpdateMapping()
    {
        ReviewElastic::updateMapping();
        echo "Review mapping updated successfully.\n";
    }
}
