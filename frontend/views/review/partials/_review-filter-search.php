<?php

use frontend\helpers\Html;
use frontend\models\ReviewElasticSearch;
use yii\widgets\ActiveForm;
?>
<section class="filterSidebarSection" id="reviewFilterDSection">
    <p class="foundesults row">Found <?= $totalReviewCount ?? '' ?> Reviews
        <?php if (!empty($selectedFilters)): ?>
            <span id="clearAllReviews" class="clearAll">Clear All</span>
        <?php endif; ?>
    </p>

    <?php if (!empty($selectedFilters)): ?>
        <div class="filterCategory">
            <p class="filterCategoryReviewName">Selected Filters</p>
            <div id="selectedReviewFilters" class="filterReviewDiv">
                <?php foreach ($selectedFilters as $filter): ?>
                    <button id="<?= $filter['slug'] ?>" data-attr="<?= $filter['mapped_field'] ?>"><?= $filter['name'] ?> <i class="spriteIcon closeIcon remove-review-filter"></i></button>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>

    <?php $form = ActiveForm::begin([
        'id' => 'review-filter-form'
    ]); ?>

    <?php foreach ($filters as $key => $items): ?>
        <?php if (isset(ReviewElasticSearch::$propertyMapping[$key])): ?>
            <div class="filterCategory">
                <p class="filterCategoryReviewName"><?= $key ?></p>
                <div class="filterReviewDiv">
                    <div class="filterReviewSearch filterSearchValue">
                        <input type="text" placeholder="Search options">
                        <?= $form->field($model, ReviewElasticSearch::$propertyMapping[$key])->checkboxList($items, [
                            'tag' => 'ul',
                            'item' => function ($index, $label, $name, $checked, $value) {
                                $input = Html::checkbox($name, $checked, ['value' => $value, 'id' => $value]);
                                $label = Html::label($label, $value);
                                return "<li>{$input}{$label}</li>";
                            }
                        ])->label(false) ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    <?php endforeach; ?>

    <?php ActiveForm::end(); ?>
</section>