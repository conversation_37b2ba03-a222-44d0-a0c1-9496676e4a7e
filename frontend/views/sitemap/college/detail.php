<?php

use common\services\CollegeService;
use frontend\helpers\Url;
?>

<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
    <?php if (empty($page)): ?>
        <?php for ($i = 1; $i <= $data; $i++): ?>
            <url>
                <loc><?= Url::base(true) . '/sitemap/' . $category . '/' . $slug . '/' . $i ?></loc>
            </url>
        <?php endfor; ?>
    <?php else: ?>
        <?php
        // dd($data);
        // $collegeSlugs = (array) CollegeService::getCollegeSlugsForSitemap($data);
        // dd($collegeSlugs);
        foreach ($data as $val):
            // dd($val);
            $ciPI = ['ci', 'pi'];
            $getCollegslug = $val->sub_page == 'info' ? $val->slug : substr(str_replace($val->sub_page, '', $val->slug), 0, -1);
            // if (!in_array($getCollegslug, $collegeSlugs) && !in_array($val->sub_page, $ciPI)) {
            //     continue;
            // }
            ?>
            <url>
                <?php if (in_array($val->sub_page, $ciPI)): ?>
                    <loc><?= $val->domain . '/college/' . $val->slug ?></loc>
                <?php else: ?>
                    <loc><?= $val->domain . '/' . $val->slug ?></loc>
                <?php endif; ?>
                <lastmod>
                    <?php if (in_array($val->sub_page, $ciPI)): ?>
                        <?= date(DATE_ATOM, strtotime($val->sitemap_update)) ?>
                    <?php else: ?>
                        <?= date(DATE_ATOM, strtotime($val->updated_at)) ?>
                    <?php endif ;?>
                </lastmod>
                <changefreq><?= $val->change_freq ?></changefreq>
                <priority><?= $val->priority ?></priority>
            </url>
        <?php endforeach; ?>
    <?php endif; ?>

    <?php if (!empty($ciPiData)): ?>
        <?php foreach ($ciPiData as $key => $value):
            $model = $slug == 'ci' ? $value->collegeCourseContent : $value->collegeProgram;
            $college = $model->college->slug;
            $course = $model->course->slug;
            ?>
            <url>
                <loc><?= Url::base(true) . '/sitemap/' . $category . '/' . $college . '/' .
                            $course . '/', $slug . '-' . $value->sub_page . '-' . $value->id ?></loc>
                <lastmod><?= date(DATE_ATOM, strtotime($value->sitemap_update)) ?></lastmod>
                <changefreq>monthly</changefreq>
                <priority>0.5</priority>
            </url>
        <?php endforeach; ?>
    <?php endif; ?>
</urlset>