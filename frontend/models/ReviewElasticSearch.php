<?php

namespace frontend\models;

use common\models\Review;
use common\models\ReviewElastic;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use yii\helpers\ArrayHelper;
use yii\elasticsearch\Query;
use yii\web\NotFoundHttpException;
use common\models\ReviewFilter;

class ReviewElasticSearch extends Review
{
    public $sort = 'review_created_at';
    
    public $city;
    
    public $state;
    
    public $course;
    
    public $stream;
    
    public $batch;
    
    public $selectedFilters = [];
    
    public $totalCount;
    
    public $availableFacets;
    
    public $currentPage = 1;
    
    protected $perPage = 10;

    public static $propertyMapping = [
        'City' => 'city',
        'State' => 'state',
        'Courses' => 'course',
        'Streams' => 'stream',
        'Batch' => 'batch',
    ];

    /**
     * Elasticsearch field mappings for filters
     */
    private static $_elasticFieldMapping = [
        'state' => 'state_slug',
        'city' => 'city_slug',
        'course' => 'course_slug',
        'stream' => 'stream_slug',
        'batch' => 'batch',
    ];

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['sort', 'city', 'state', 'course', 'stream', 'batch'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied using Elasticsearch
     *
     * @param array $params
     * @param array $bodyParams
     *
     * @return ActiveDataProvider
     */
    public function search($params, $bodyParams = [])
    {
        if (isset($bodyParams['page']) && !empty($bodyParams['page']) && $bodyParams['page'] != 'undefined') {
            $this->currentPage = (int) $bodyParams['page'];
            unset($bodyParams['page']);
        }

        if (isset($bodyParams['sortBy']) && !empty($bodyParams['sortBy']) && $bodyParams['sortBy'] != 'undefined') {
            $this->sort = $bodyParams['sortBy'];
            unset($bodyParams['sortBy']);
        }

        $this->loadRequests($params, $bodyParams);

        $query = ReviewElastic::find();
        $filters = $this->buildSearchFilters();

        // Apply filters to query
        if (!empty($filters['bool']['must'])) {
            $query->query($filters);
        }

        // Apply sorting
        $sortField = $this->getSortField();
        if ($sortField) {
            $query->orderBy($sortField);
        }

        // Set pagination limit
        $query->limit($this->perPage);

        // Load facets
        $this->loadElasticFacets();
        $this->selectedFilters = ArrayHelper::index($this->selectedFilters, 'slug');

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => [
                'pageSize' => $this->perPage,
                'page' => $this->currentPage - 1,
            ],
        ]);

        return $dataProvider;
    }
    
    /**
     * Get sort field for Elasticsearch
     */
    private function getSortField()
    {
        switch ($this->sort) {
            case 'lowest_rating':
                return ['review_overall_rating' => SORT_ASC];
            case 'highest_rating':
                return ['review_overall_rating' => SORT_DESC];
            case 'oldest_rating':
                return ['review_created_at' => SORT_ASC];
            case 'newest_rating':
            case 'review_created_at':
            default:
                return ['review_created_at' => SORT_DESC];
        }
    }

    /**
     * Check if filter values are valid (non-empty)
     */
    private function hasValidFilterValues($value)
    {
        if (empty($value)) {
            return false;
        }

        if (is_array($value)) {
            return !empty(array_filter($value));
        }

        return !empty(trim($value));
    }

    /**
     * Build search filters for the main query
     */
    private function buildSearchFilters()
    {
        $filters = ['bool' => ['must' => []]];

        // Base filter for active reviews
        $filters['bool']['must'][] = ['match' => ['status' => Review::STATUS_APPROVED]];

        $this->applyFiltersExcept($filters, null);

        return $filters;
    }

    /**
     * Load facets using Elasticsearch aggregations
     */
    private function loadElasticFacets()
    {
        foreach (self::$_elasticFieldMapping as $facetName => $fieldName) {
            $this->availableFacets[$facetName] = $this->loadGenericFacet($fieldName, $facetName);
        }
    }

    /**
     * Generic method to load facets from Elasticsearch
     */
    private function loadGenericFacet($field, $excludeFilter)
    {
        $query = new Query();
        $filters = ['bool' => ['must' => []]];

        // Base filter
        $filters['bool']['must'][] = ['match' => ['status' => Review::STATUS_APPROVED]];

        // Apply all filters except the one we're excluding
        $this->applyFiltersExcept($filters, $excludeFilter);

        $query->query($filters);
        $query->addAggregate('facets', ['terms' => ['field' => $field, 'size' => 100]]);
        $query->from('reviews', 'review');
        $query->limit(0);

        $command = $query->createCommand();
        $result = $command->search();

        $facets = [];
        if (isset($result['aggregations']['facets']['buckets'])) {
            foreach ($result['aggregations']['facets']['buckets'] as $bucket) {
                $facets[] = [
                    '_id' => $bucket['key'],
                    'count' => $bucket['doc_count']
                ];
            }
        }

        return $facets;
    }

    /**
     * Apply all filters except the specified one
     */
    private function applyFiltersExcept(&$filters, $excludeFilter)
    {
        $filterData = [
            'state' => $this->state,
            'city' => $this->city,
            'batch' => $this->batch,
            'course' => $this->course,
            'stream' => $this->stream,
        ];

        foreach ($filterData as $filterName => $value) {
            if ($filterName === $excludeFilter || empty($value)) {
                continue;
            }

            // Validate filter values for course and stream
            if (in_array($filterName, ['course', 'stream']) && !$this->hasValidFilterValues($value)) {
                continue;
            }

            $values = is_array($value) ? $value : [$value];

            // Transform batch values to integers
            if ($filterName === 'batch') {
                $values = array_map('intval', $values);
            }

            $fieldName = self::$_elasticFieldMapping[$filterName];
            $filters['bool']['must'][] = ['terms' => [$fieldName => $values]];
        }
    }





    /**
     * Load parameters from URL and body params
     */
    public function loadRequests($params, $bodyParams)
    {
        if ((empty($this->loadParams($params)) && !empty($params))) {
            throw new NotFoundHttpException();
        }

        $this->loadParams($params);
        $this->loadBodyParams($bodyParams);

        return $this;
    }

    /**
     * Load parameters from URL params
     */
    public function loadParams(array $params)
    {
        if (empty($params)) {
            return [];
        }

        foreach ($params as $value) {
            $queries = ReviewFilter::find()->where(['in', 'slug', explode(',', $value)])->with('reviewFilterGroup')->all();

            if (!$queries) {
                return [];
            }

            foreach ($queries as $query) {
                $this->setProperty($query);
            }
        }

        return $this;
    }

    /**
     * Load parameters from body params
     */
    public function loadBodyParams(array $bodyParams)
    {
        $searchParams = $bodyParams['ReviewElasticSearch'] ?? [];

        if (empty($searchParams)) {
            return [];
        }

        foreach ($searchParams as $value) {
            if (empty($value)) {
                continue;
            }

            $queries = ReviewFilter::find()->where(['in', 'slug', $value])->with('reviewFilterGroup')->all();
            if (!$queries) {
                continue;
            }

            foreach ($queries as $query) {
                if ($query->parent_id) {
                    $parent = $query->parent;
                    $this->setProperty($parent);
                }

                $this->setProperty($query);
            }
        }

        return $this;
    }

    /**
     * Set selected filters for display
     */
    public function setSelectedFilters($model)
    {
        $this->selectedFilters[] = [
            'id' => $model->id,
            'name' => $model->name,
            'slug' => $model->slug,
            'mapped_field' => $model->reviewFilterGroup->mapped_field,
            'parent_id' => $model->parent_id,
            'filterGroup_name' => $model->reviewFilterGroup->name,
            'value_type' => $model->reviewFilterGroup->value_type,
            'rule' => json_decode($model->reviewFilterGroup->rule, true),
            'url_position' => $model->reviewFilterGroup->url_position
        ];

        return $this;
    }

    /**
     * Set property based on filter object
     */
    public function setProperty($filterObj)
    {
        if (strtolower($filterObj->reviewFilterGroup->value_type) == 'checkbox') {
            $this->{$filterObj->reviewFilterGroup->mapped_field}[] = $filterObj->slug;
        } else {
            $this->{$filterObj->reviewFilterGroup->mapped_field} = $filterObj->slug;
        }

        $this->setSelectedFilters($filterObj);

        if ($filterObj->parent_id) {
            $this->setProperty($filterObj->parent);
        }

        return $this;
    }
}
